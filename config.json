{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "RZA Worldgen Pack", "packs": {"behaviorPack": "./packs/BP", "resourcePack": "./packs/RP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"json_cleaner": {"url": "github.com/Bedrock-OSS/regolith-filters", "version": "1.1.1"}}, "formatVersion": "1.4.0", "profiles": {"build": {"export": {"readOnly": false, "target": "local"}, "filters": []}, "default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}}}}